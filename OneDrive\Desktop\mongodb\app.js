// const express=require('express');
// const app=express();
// const userModel=require('./usermodel');
// app.get('/',(req,res)=>{
//     res.send("hii");
// })

// app.get('/create',async (req,res)=>{
//    let createduser= await userModel.create({
//         name:"sakshi",
//         username:"sakshi@23",
//         email:"<EMAIL>"

//     });

//     req.send(createduser);
// })
// app.listen(3000);
// console.log("sbb shi h");

//Bhai cookie aese set hoga smjhe!!!
// const express=require('express');
// const app=express();
// app.get('/',(req,res)=>{
//     res.cookie('name','sakshi');
//     res.send("hii");
// });

// // read aese hoga cookie
// app.get('/read',function(req,res){
//     res.send(req.cookies);
//     res.send("read page");
// });


// console.log("sbb shi h");


// bcrypt -do chiz krta hh encrypt and decrypt
// aese hoga encrypt yaani real password chuoa dena
// salt-ek random string hota hh
// jisko hash krdiya jata h bhaiya
const express=require('express');
const app=express();
const bcrypt=require('bcrypt');
app.get('/',function(req,res){
    bcrypt.genSalt(saltRounds,function(err,salt){
    bcrypt.hash(myPlainPassword,salt,function(err,hash){
    console.log(hash);
    });
    });
    
})


